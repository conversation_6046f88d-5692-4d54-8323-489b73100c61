#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <string.h>
#include <windows.h>
#include <mmsystem.h>
#include <process.h>

#pragma comment(lib, "winmm.lib")

#define MIDI_NOTE_OFF       0x80
#define MIDI_NOTE_ON        0x90
#define MIDI_AFTERTOUCH     0xA0
#define MIDI_CONTROL_CHANGE 0xB0
#define MIDI_PROGRAM_CHANGE 0xC0
#define MIDI_CHANNEL_PRESSURE 0xD0
#define MIDI_PITCH_BEND     0xE0
#define MIDI_SYSTEM_EXCLUSIVE 0xF0
#define MIDI_META_EVENT     0xFF

#define META_SET_TEMPO      0x51
#define META_END_OF_TRACK   0x2F
#define MAX_TRACKS 20000

#define EVENT_BUFFER_CAPACITY 1048576 
#define TEMPO_CHANGE_FLAG 0xFF000000
#define BATCH_SIZE 512  // Increased batch size for better throughput
#define PITCH_BEND_NEUTRAL 8192

// --- Data Structures ---
typedef struct {
    uint32_t playback_time_us; 
    uint32_t message;          
} BufferedEvent;

typedef struct {
    BufferedEvent events[EVENT_BUFFER_CAPACITY];
    volatile long head;
    volatile long tail;
    CRITICAL_SECTION cs;
    CONDITION_VARIABLE not_full;
    CONDITION_VARIABLE not_empty;
} EventBuffer;

typedef struct TrackState {
    const uint8_t *data_start;  // Memory-mapped file start
    const uint8_t *data_end;    // Memory-mapped file end
    const uint8_t *data_ptr;    // Current position in memory-mapped data
    uint32_t next_event_time;
    uint8_t running_status;
    int track_id, finished;
} TrackState;

typedef struct {
    TrackState* tracks;
    int num_tracks;
} ProducerThreadArgs;

typedef struct {
    TrackState** nodes;
    int size;
    int capacity;
} MinHeap;

// --- Global State ---
static EventBuffer g_event_buffer;
static HMIDIOUT g_hMidiOut = NULL;
static LARGE_INTEGER g_start_time, g_frequency;
static volatile int g_stop = 0;
static CRITICAL_SECTION g_midi_cs, g_seek_cs;
static volatile int g_seek_request = 0;
static volatile int64_t g_seek_offset_us = 0;
static volatile uint32_t g_tempo = 500000;
static uint16_t g_ticks_per_quarter = 480;
static volatile int g_producer_finished = 0;
static volatile uint64_t g_consumer_playback_time_us = 0;
static uint8_t g_channel_program[16], g_channel_controller[16][128], g_notes_on[16][128];
static uint16_t g_channel_pitch_bend[16];

// Memory-mapped file globals
static HANDLE g_file_handle = INVALID_HANDLE_VALUE;
static HANDLE g_mapping_handle = NULL;
static const uint8_t* g_file_data = NULL;
static SIZE_T g_file_size = 0;

// --- Min-Heap Implementation (Optimized) ---
static inline void swap_nodes(TrackState** a, TrackState** b) { 
    TrackState* temp = *a; *a = *b; *b = temp; 
}

static inline void heapify_up(MinHeap* h, int index) {
    while (index > 0) {
        int parent = (index - 1) >> 1;  // Faster division by 2
        if (h->nodes[parent]->next_event_time <= h->nodes[index]->next_event_time) break;
        swap_nodes(&h->nodes[parent], &h->nodes[index]);
        index = parent;
    }
}

static inline void heapify_down(MinHeap* h, int index) {
    while (1) {
        int left = (index << 1) + 1;  // Faster multiplication by 2
        int right = left + 1;
        int smallest = index;
        
        if (left < h->size && h->nodes[left]->next_event_time < h->nodes[smallest]->next_event_time) 
            smallest = left;
        if (right < h->size && h->nodes[right]->next_event_time < h->nodes[smallest]->next_event_time) 
            smallest = right;
        
        if (smallest == index) break;
        
        swap_nodes(&h->nodes[index], &h->nodes[smallest]);
        index = smallest;
    }
}

MinHeap* heap_create(int capacity) {
    MinHeap* h = (MinHeap*)malloc(sizeof(MinHeap));
    if (!h) return NULL;
    h->nodes = (TrackState**)malloc(sizeof(TrackState*) * capacity);
    if (!h->nodes) { free(h); return NULL; }
    h->size = 0; 
    h->capacity = capacity;
    return h;
}

static inline void heap_insert(MinHeap* h, TrackState* track) {
    if (h->size >= h->capacity) return;
    h->nodes[h->size] = track;
    heapify_up(h, h->size++);
}

static inline TrackState* heap_extract_min(MinHeap* h) {
    if (h->size == 0) return NULL;
    TrackState* min_node = h->nodes[0];
    h->nodes[0] = h->nodes[--h->size];
    if (h->size > 0) heapify_down(h, 0);
    return min_node;
}

void heap_destroy(MinHeap* h) { 
    if (h) { 
        free(h->nodes); 
        free(h); 
    } 
}

// --- Memory-Mapped File I/O Functions ---
static inline uint8_t read_byte(TrackState *track) {
    if (track->data_ptr >= track->data_end) {
        track->finished = 1;
        return 0;
    }
    return *(track->data_ptr++);
}

static inline uint32_t read_vlq(TrackState *track) {
    uint32_t value = 0;
    uint8_t byte;
    do {
        if (track->data_ptr >= track->data_end) {
            track->finished = 1;
            break;
        }
        byte = *(track->data_ptr++);
        value = (value << 7) | (byte & 0x7F);
    } while (byte & 0x80);
    return value;
}

static inline void skip_bytes(TrackState *track, uint32_t count) {
    if (track->data_ptr + count > track->data_end) {
        track->data_ptr = track->data_end;
        track->finished = 1;
    } else {
        track->data_ptr += count;
    }
}

// --- Timing and MIDI Output (Optimized) ---
static inline uint64_t ticks_to_microseconds(uint32_t ticks, uint32_t tempo) { 
    return ((uint64_t)ticks * tempo) / g_ticks_per_quarter; 
}

static inline uint64_t get_time_microseconds() { 
    LARGE_INTEGER now; 
    QueryPerformanceCounter(&now); 
    return ((now.QuadPart - g_start_time.QuadPart) * 1000000LL) / g_frequency.QuadPart; 
}

static inline void send_midi_msg(uint32_t msg) { 
    if (g_hMidiOut) { 
        EnterCriticalSection(&g_midi_cs); 
        midiOutShortMsg(g_hMidiOut, msg); 
        LeaveCriticalSection(&g_midi_cs); 
    } 
}

// --- Memory Mapping Functions ---
int init_memory_mapping(const char* filename) {
    g_file_handle = CreateFileA(filename, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    if (g_file_handle == INVALID_HANDLE_VALUE) {
        printf("Error: Cannot open file %s (Error: %lu)\n", filename, GetLastError());
        return 0;
    }

    LARGE_INTEGER file_size;
    if (!GetFileSizeEx(g_file_handle, &file_size)) {
        printf("Error: Cannot get file size (Error: %lu)\n", GetLastError());
        CloseHandle(g_file_handle);
        return 0;
    }
    g_file_size = (SIZE_T)file_size.QuadPart;

    g_mapping_handle = CreateFileMappingA(g_file_handle, NULL, PAGE_READONLY, 0, 0, NULL);
    if (!g_mapping_handle) {
        printf("Error: Cannot create file mapping (Error: %lu)\n", GetLastError());
        CloseHandle(g_file_handle);
        return 0;
    }

    g_file_data = (const uint8_t*)MapViewOfFile(g_mapping_handle, FILE_MAP_READ, 0, 0, 0);
    if (!g_file_data) {
        printf("Error: Cannot map view of file (Error: %lu)\n", GetLastError());
        CloseHandle(g_mapping_handle);
        CloseHandle(g_file_handle);
        return 0;
    }

    return 1;
}

void cleanup_memory_mapping() {
    if (g_file_data) {
        UnmapViewOfFile(g_file_data);
        g_file_data = NULL;
    }
    if (g_mapping_handle) {
        CloseHandle(g_mapping_handle);
        g_mapping_handle = NULL;
    }
    if (g_file_handle != INVALID_HANDLE_VALUE) {
        CloseHandle(g_file_handle);
        g_file_handle = INVALID_HANDLE_VALUE;
    }
}

// --- Forward Declarations ---
void process_event_for_seek(TrackState* track, uint32_t* current_tempo);
void handle_seek(TrackState* tracks, int num_tracks, uint64_t* producer_time_us, uint32_t* last_event_tick, uint32_t* current_tempo, MinHeap** pq);
void commit_batch(BufferedEvent* batch, int count);

// --- Consumer Thread (Optimized) ---
unsigned __stdcall consumer_thread(void* arg) {
    BufferedEvent local_batch[BATCH_SIZE];
    int events_in_batch = 0, batch_pos = 0;
    
    // Set thread priority for better timing accuracy
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_TIME_CRITICAL);
    
    while (!g_stop) {
        // Refill batch if empty
        if (batch_pos >= events_in_batch) {
            EnterCriticalSection(&g_event_buffer.cs);
            while (g_event_buffer.head == g_event_buffer.tail && !g_producer_finished && !g_stop) {
                SleepConditionVariableCS(&g_event_buffer.not_empty, &g_event_buffer.cs, INFINITE);
            }
            
            if (g_stop) {
                LeaveCriticalSection(&g_event_buffer.cs);
                break;
            }
            
            if (g_event_buffer.head == g_event_buffer.tail && g_producer_finished) {
                LeaveCriticalSection(&g_event_buffer.cs);
                Sleep(50);
                continue;
            }
            
            long head = g_event_buffer.head;
            long tail = g_event_buffer.tail;
            long count = (tail >= head) ? (tail - head) : (EVENT_BUFFER_CAPACITY - (head - tail));
            events_in_batch = (count < BATCH_SIZE) ? (int)count : BATCH_SIZE;
            
            // Optimized circular buffer copy
            if (head + events_in_batch > EVENT_BUFFER_CAPACITY) {
                long first_chunk_size = EVENT_BUFFER_CAPACITY - head;
                memcpy(local_batch, &g_event_buffer.events[head], first_chunk_size * sizeof(BufferedEvent));
                memcpy(&local_batch[first_chunk_size], &g_event_buffer.events[0], (events_in_batch - first_chunk_size) * sizeof(BufferedEvent));
            } else {
                memcpy(local_batch, &g_event_buffer.events[head], events_in_batch * sizeof(BufferedEvent));
            }
            
            g_event_buffer.head = (head + events_in_batch) % EVENT_BUFFER_CAPACITY;
            batch_pos = 0;
            WakeConditionVariable(&g_event_buffer.not_full);
            LeaveCriticalSection(&g_event_buffer.cs);
        }
        
        BufferedEvent event = local_batch[batch_pos++];
        g_consumer_playback_time_us = event.playback_time_us;
        
        // High-precision timing
        uint64_t time_now = get_time_microseconds();
        if (event.playback_time_us > time_now) {
            uint64_t diff = event.playback_time_us - time_now;
            if (diff > 2000) {
                Sleep((DWORD)((diff - 1000) / 1000));
            }
            // Busy wait for final precision
            while (get_time_microseconds() < event.playback_time_us && !g_stop);
        }
        
        if (g_stop) break;
        
        // Process event
        if ((event.message & TEMPO_CHANGE_FLAG) == TEMPO_CHANGE_FLAG) {
            g_tempo = event.message & ~TEMPO_CHANGE_FLAG;
        } else {
            send_midi_msg(event.message);
        }
    }
    return 0;
}

// --- Producer Thread (Optimized) ---
unsigned __stdcall producer_thread(void* arg) {
    ProducerThreadArgs* args = (ProducerThreadArgs*)arg;
    TrackState* tracks = args->tracks;
    int num_tracks = args->num_tracks;
    BufferedEvent local_batch[BATCH_SIZE];
    int batch_count = 0;

    // Set high thread priority
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);

    MinHeap* pq = heap_create(num_tracks);
    if (!pq) {
        printf("Error: Failed to create priority queue\n");
        return 1;
    }

    // Initialize heap with non-empty tracks
    for (int i = 0; i < num_tracks; i++) {
        if (tracks[i].data_ptr < tracks[i].data_end) {
            tracks[i].next_event_time = read_vlq(&tracks[i]);
            if (!tracks[i].finished) {
                heap_insert(pq, &tracks[i]);
            }
        } else {
            tracks[i].finished = 1;
        }
    }

    uint64_t producer_time_us = 0;
    uint32_t last_event_tick = 0;
    uint32_t current_tempo = 500000;

    while (!g_stop) {
        // Handle seek requests
        if (g_seek_request) {
            commit_batch(local_batch, batch_count);
            batch_count = 0;
            handle_seek(tracks, num_tracks, &producer_time_us, &last_event_tick, &current_tempo, &pq);
            continue;
        }

        if (pq->size == 0) {
            // Song finished
            if (!g_producer_finished) {
                commit_batch(local_batch, batch_count);
                batch_count = 0;
                g_producer_finished = 1;
                EnterCriticalSection(&g_event_buffer.cs);
                WakeConditionVariable(&g_event_buffer.not_empty);
                LeaveCriticalSection(&g_event_buffer.cs);
            }
            Sleep(50);
            continue;
        }
        
        TrackState* track = heap_extract_min(pq);
        uint32_t current_event_tick = track->next_event_time;
        
        // Update timing
        producer_time_us += ticks_to_microseconds(current_event_tick - last_event_tick, current_tempo);
        last_event_tick = current_event_tick;
        
        // Read MIDI event
        uint8_t status = read_byte(track);
        if (track->finished) continue;

        // Handle running status
        if (status < 0x80) {
            track->data_ptr--; // Backup
            status = track->running_status;
        } else {
            track->running_status = status;
        }
        
        BufferedEvent new_event = { .playback_time_us = (uint32_t)producer_time_us };
        int event_is_valid = 1;

        // Process different event types
        if (status == MIDI_META_EVENT) {
            uint8_t meta_type = read_byte(track);
            uint32_t length = read_vlq(track);
            
            if (meta_type == META_SET_TEMPO && length >= 3) {
                current_tempo = (read_byte(track) << 16) | (read_byte(track) << 8) | read_byte(track);
                new_event.message = TEMPO_CHANGE_FLAG | current_tempo;
                length -= 3;
            } else {
                event_is_valid = 0;
            }
            
            if (length > 0) {
                skip_bytes(track, length);
            }
            
            if (meta_type == META_END_OF_TRACK) {
                track->finished = 1;
            }
        } else if (status == MIDI_SYSTEM_EXCLUSIVE || status == 0xF7) {
            uint32_t length = read_vlq(track);
            skip_bytes(track, length);
            event_is_valid = 0;
        } else if ((status & 0xF0) >= 0x80 && (status & 0xF0) <= 0xE0) {
            uint8_t data1 = read_byte(track);
            uint8_t data2 = 0;
            
            if ((status & 0xF0) != MIDI_PROGRAM_CHANGE && (status & 0xF0) != MIDI_CHANNEL_PRESSURE) {
                data2 = read_byte(track);
            }
            
            if (!track->finished) {
                new_event.message = status | (data1 << 8) | (data2 << 16);
            } else {
                event_is_valid = 0;
            }
        } else {
            event_is_valid = 0;
        }
        
        // Schedule next event for this track
        if (!track->finished) {
            track->next_event_time += read_vlq(track);
            if (!track->finished) {
                heap_insert(pq, track);
            }
        }
        
        // Add valid events to batch
        if (event_is_valid && !track->finished) {
            local_batch[batch_count++] = new_event;
            if (batch_count == BATCH_SIZE) {
                commit_batch(local_batch, batch_count);
                batch_count = 0;
            }
        }
    }
    
    commit_batch(local_batch, batch_count);
    g_producer_finished = 1;
    heap_destroy(pq);
    
    EnterCriticalSection(&g_event_buffer.cs);
    WakeConditionVariable(&g_event_buffer.not_empty);
    LeaveCriticalSection(&g_event_buffer.cs);
    
    return 0;
}

void commit_batch(BufferedEvent* batch, int count) {
    if (count == 0) return;
    
    EnterCriticalSection(&g_event_buffer.cs);
    
    while (1) {
        long head = g_event_buffer.head;
        long tail = g_event_buffer.tail;
        long free_space = (head > tail) ? (head - tail - 1) : (EVENT_BUFFER_CAPACITY - (tail - head) - 1);
        
        if (free_space >= count) break;
        
        SleepConditionVariableCS(&g_event_buffer.not_full, &g_event_buffer.cs, 100);
        if (g_stop) {
            LeaveCriticalSection(&g_event_buffer.cs);
            return;
        }
    }
    
    long tail = g_event_buffer.tail;
    if (tail + count > EVENT_BUFFER_CAPACITY) {
        long first_chunk_size = EVENT_BUFFER_CAPACITY - tail;
        memcpy(&g_event_buffer.events[tail], batch, first_chunk_size * sizeof(BufferedEvent));
        memcpy(&g_event_buffer.events[0], &batch[first_chunk_size], (count - first_chunk_size) * sizeof(BufferedEvent));
    } else {
        memcpy(&g_event_buffer.events[tail], batch, count * sizeof(BufferedEvent));
    }
    
    g_event_buffer.tail = (tail + count) % EVENT_BUFFER_CAPACITY;
    WakeConditionVariable(&g_event_buffer.not_empty);
    LeaveCriticalSection(&g_event_buffer.cs);
}

void handle_seek(TrackState* tracks, int num_tracks, uint64_t* producer_time_us, uint32_t* last_event_tick, uint32_t* current_tempo, MinHeap** pq) {
    EnterCriticalSection(&g_seek_cs);
    
    g_producer_finished = 0; // Revive the producer
    int64_t target_us = (int64_t)g_consumer_playback_time_us + g_seek_offset_us;
    if (target_us < 0) target_us = 0;
    g_seek_offset_us = 0;
    
    printf("\rSeeking to %.2f seconds...         \n", target_us / 1000000.0);
    fflush(stdout);

    // Clear event buffer
    EnterCriticalSection(&g_event_buffer.cs);
    g_event_buffer.head = g_event_buffer.tail = 0;
    g_consumer_playback_time_us = target_us;
    WakeAllConditionVariable(&g_event_buffer.not_full);
    LeaveCriticalSection(&g_event_buffer.cs);
    
    // Reset MIDI state
    for (int i = 0; i < 16; i++) {
        send_midi_msg(MIDI_CONTROL_CHANGE | i | (123 << 8)); // All notes off
        send_midi_msg(MIDI_CONTROL_CHANGE | i | (121 << 8)); // Reset all controllers
    }
    
    memset(g_channel_program, 0, 16);
    memset(g_channel_controller, 0, 16 * 128);
    memset(g_notes_on, 0, 16 * 128);
    *current_tempo = 500000;
    for (int i = 0; i < 16; i++) {
        g_channel_pitch_bend[i] = PITCH_BEND_NEUTRAL;
    }

    // Reset all tracks to beginning
    for (int i = 0; i < num_tracks; i++) {
        tracks[i].data_ptr = tracks[i].data_start;
        tracks[i].finished = 0;
        tracks[i].running_status = 0;
    }

    // Rebuild priority queue
    heap_destroy(*pq);
    *pq = heap_create(num_tracks);
    
    for (int i = 0; i < num_tracks; i++) {
        if (tracks[i].data_ptr < tracks[i].data_end) {
            tracks[i].next_event_time = read_vlq(&tracks[i]);
            if (!tracks[i].finished) {
                heap_insert(*pq, &tracks[i]);
            }
        } else {
            tracks[i].finished = 1;
        }
    }
    
    // Fast-forward to target time
    uint64_t sim_time_us = 0;
    uint32_t sim_last_tick = 0;
    
    while (sim_time_us < (uint64_t)target_us && !g_stop) {
        if ((*pq)->size == 0) break;
        
        TrackState* track = heap_extract_min(*pq);
        uint64_t time_delta_us = ticks_to_microseconds(track->next_event_time - sim_last_tick, *current_tempo);
        
        if (sim_time_us + time_delta_us >= (uint64_t)target_us) {
            heap_insert(*pq, track);
            break;
        }
        
        sim_time_us += time_delta_us;
        sim_last_tick = track->next_event_time;
        
        process_event_for_seek(track, current_tempo);
        
        if (!track->finished) {
            track->next_event_time += read_vlq(track);
            if (!track->finished) {
                heap_insert(*pq, track);
            }
        }
    }

    // Restore MIDI state
    for (int ch = 0; ch < 16; ch++) {
        send_midi_msg(MIDI_PROGRAM_CHANGE | ch | (g_channel_program[ch] << 8));
        
        uint8_t lsb = g_channel_pitch_bend[ch] & 0x7F;
        uint8_t msb = (g_channel_pitch_bend[ch] >> 7) & 0x7F;
        send_midi_msg(MIDI_PITCH_BEND | ch | (lsb << 8) | (msb << 16));
        
        for (int c = 0; c < 128; c++) {
            if (g_channel_controller[ch][c] != 0) {
                send_midi_msg(MIDI_CONTROL_CHANGE | ch | (c << 8) | (g_channel_controller[ch][c] << 16));
            }
        }
        
        for (int n = 0; n < 128; n++) {
            if (g_notes_on[ch][n]) {
                send_midi_msg(MIDI_NOTE_ON | ch | (n << 8) | (100 << 16));
            }
        }
    }
    
    *producer_time_us = sim_time_us;
    *last_event_tick = sim_last_tick;
    g_tempo = *current_tempo;
    
    // Reset timing reference
    QueryPerformanceCounter(&g_start_time);
    g_start_time.QuadPart -= (sim_time_us * g_frequency.QuadPart) / 1000000LL;
    
    g_seek_request = 0;
    LeaveCriticalSection(&g_seek_cs);
}

void process_event_for_seek(TrackState* track, uint32_t* current_tempo) {
    if (track->finished || track->data_ptr >= track->data_end) {
        track->finished = 1;
        return;
    }
    
    uint8_t status = read_byte(track);
    if (track->finished) return;
    
    if (status < 0x80) {
        track->data_ptr--; // Backup
        status = track->running_status;
    } else {
        track->running_status = status;
    }
    
    uint8_t channel = status & 0x0F;
    
    if (status == MIDI_META_EVENT) {
        uint8_t meta_type = read_byte(track);
        uint32_t length = read_vlq(track);
        
        if (meta_type == META_SET_TEMPO && length >= 3) {
            *current_tempo = (read_byte(track) << 16) | (read_byte(track) << 8) | read_byte(track);
            length -= 3;
        }
        
        if (length > 0) {
            skip_bytes(track, length);
        }
        
        if (meta_type == META_END_OF_TRACK) {
            track->finished = 1;
        }
    } else if (status == MIDI_SYSTEM_EXCLUSIVE || status == 0xF7) {
        uint32_t length = read_vlq(track);
        skip_bytes(track, length);
    } else if ((status & 0xF0) >= 0x80 && (status & 0xF0) <= 0xE0) {
        uint8_t data1 = read_byte(track);
        uint8_t data2 = 0;
        
        if ((status & 0xF0) != MIDI_PROGRAM_CHANGE && (status & 0xF0) != MIDI_CHANNEL_PRESSURE) {
            data2 = read_byte(track);
        }
        
        if (!track->finished) {
            switch (status & 0xF0) {
                case MIDI_NOTE_ON:
                    g_notes_on[channel][data1] = (data2 > 0);
                    break;
                case MIDI_NOTE_OFF:
                    g_notes_on[channel][data1] = 0;
                    break;
                case MIDI_PROGRAM_CHANGE:
                    g_channel_program[channel] = data1;
                    break;
                case MIDI_CONTROL_CHANGE:
                    g_channel_controller[channel][data1] = data2;
                    break;
                case MIDI_PITCH_BEND:
                    g_channel_pitch_bend[channel] = (data2 << 7) | data1;
                    break;
            }
        }
    }
}

// --- Main MIDI File Processing ---
uint32_t read_be32(const uint8_t* data) {
    return (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3];
}

uint16_t read_be16(const uint8_t* data) {
    return (data[0] << 8) | data[1];
}

int play_midi_file(const char* filename) {
    if (!init_memory_mapping(filename)) {
        return 1;
    }

    if (midiOutOpen(&g_hMidiOut, MIDI_MAPPER, 0, 0, 0) != MMSYSERR_NOERROR) {
        printf("Error: Cannot open MIDI output device\n");
        cleanup_memory_mapping();
        return 1;
    }

    // Initialize synchronization objects
    InitializeCriticalSection(&g_midi_cs);
    InitializeCriticalSection(&g_seek_cs);
    InitializeCriticalSection(&g_event_buffer.cs);
    InitializeConditionVariable(&g_event_buffer.not_empty);
    InitializeConditionVariable(&g_event_buffer.not_full);
    
    // Initialize timing
    QueryPerformanceFrequency(&g_frequency);
    QueryPerformanceCounter(&g_start_time);
    
    // Parse MIDI header
    if (g_file_size < 14) {
        printf("Error: File too small to be a valid MIDI file\n");
        cleanup_memory_mapping();
        midiOutClose(g_hMidiOut);
        return 1;
    }
    
    const uint8_t* data_ptr = g_file_data;
    
    // Check header
    if (memcmp(data_ptr, "MThd", 4) != 0) {
        printf("Error: Invalid MIDI file header\n");
        cleanup_memory_mapping();
        midiOutClose(g_hMidiOut);
        return 1;
    }
    data_ptr += 4;
    
    uint32_t header_length = read_be32(data_ptr);
    data_ptr += 4;
    
    if (header_length < 6) {
        printf("Error: Invalid MIDI header length\n");
        cleanup_memory_mapping();
        midiOutClose(g_hMidiOut);
        return 1;
    }
    
    uint16_t format = read_be16(data_ptr);
    data_ptr += 2;
    uint16_t num_tracks = read_be16(data_ptr);
    data_ptr += 2;
    g_ticks_per_quarter = read_be16(data_ptr);
    data_ptr += 2;
    
    // Skip any extra header data
    data_ptr += (header_length - 6);
    
    printf("Optimized MIDI Player v4.0 (Memory-Mapped I/O)\n");
    printf("===============================================\n");
    printf("Format: %d, Tracks: %d, TPQ: %d\n", format, num_tracks, g_ticks_per_quarter);
    printf("File size: %.2f MB\n", g_file_size / (1024.0 * 1024.0));

    // Initialize tracks
    TrackState* tracks = calloc(num_tracks, sizeof(TrackState));
    if (!tracks) {
        printf("Error: Cannot allocate memory for tracks\n");
        cleanup_memory_mapping();
        midiOutClose(g_hMidiOut);
        return 1;
    }
    
    int valid_tracks = 0;
    
    // Parse track chunks
    for (int i = 0; i < num_tracks && valid_tracks < MAX_TRACKS; i++) {
        if (data_ptr + 8 > g_file_data + g_file_size) break;
        
        // Check track header
        if (memcmp(data_ptr, "MTrk", 4) != 0) {
            printf("Warning: Invalid track header for track %d, skipping\n", i);
            data_ptr += 4;
            continue;
        }
        data_ptr += 4;
        
        uint32_t track_length = read_be32(data_ptr);
        data_ptr += 4;
        
        if (data_ptr + track_length > g_file_data + g_file_size) {
            printf("Warning: Track %d extends beyond file, truncating\n", i);
            track_length = (uint32_t)((g_file_data + g_file_size) - data_ptr);
        }
        
        // Initialize track state for memory-mapped access
        tracks[valid_tracks].data_start = data_ptr;
        tracks[valid_tracks].data_end = data_ptr + track_length;
        tracks[valid_tracks].data_ptr = data_ptr;
        tracks[valid_tracks].next_event_time = 0;
        tracks[valid_tracks].running_status = 0;
        tracks[valid_tracks].track_id = valid_tracks;
        tracks[valid_tracks].finished = 0;
        
        valid_tracks++;
        data_ptr += track_length;
    }
    
    if (valid_tracks == 0) {
        printf("Error: No valid tracks found\n");
        free(tracks);
        cleanup_memory_mapping();
        midiOutClose(g_hMidiOut);
        return 1;
    }
    
    printf("Successfully initialized %d tracks\n", valid_tracks);
    printf("Press ESC to stop. Use Left/Right Arrows to seek (±5s).\n");

    // Initialize global state
    g_event_buffer.head = g_event_buffer.tail = 0;
    g_stop = 0;
    g_seek_request = 0;
    g_seek_offset_us = 0;
    g_tempo = 500000;
    g_producer_finished = 0;
    g_consumer_playback_time_us = 0;
    
    memset(g_channel_program, 0, 16);
    memset(g_channel_controller, 0, 16 * 128);
    memset(g_notes_on, 0, 16 * 128);
    for (int i = 0; i < 16; i++) {
        g_channel_pitch_bend[i] = PITCH_BEND_NEUTRAL;
    }

    // Start threads
    ProducerThreadArgs producer_args = { tracks, valid_tracks };
    HANDLE producer_handle = (HANDLE)_beginthreadex(NULL, 0, producer_thread, &producer_args, 0, NULL);
    HANDLE consumer_handle = (HANDLE)_beginthreadex(NULL, 0, consumer_thread, NULL, 0, NULL);
    
    if (!producer_handle || !consumer_handle) {
        printf("Error: Failed to create threads\n");
        g_stop = 1;
        if (producer_handle) CloseHandle(producer_handle);
        if (consumer_handle) CloseHandle(consumer_handle);
        free(tracks);
        cleanup_memory_mapping();
        midiOutClose(g_hMidiOut);
        return 1;
    }

    // Main control loop
    int playback_finished_msg = 0;
    DWORD last_key_time = 0;
    const DWORD KEY_DEBOUNCE_MS = 200;

    while (!g_stop) {
        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            g_stop = 1;
            break;
        }
        
        DWORD current_time = GetTickCount();
        
        // Handle seeking with debouncing
        if (current_time - last_key_time > KEY_DEBOUNCE_MS) {
            if (TryEnterCriticalSection(&g_seek_cs)) {
                int seek_requested = 0;
                
                if (GetAsyncKeyState(VK_RIGHT) & 0x8000) {
                    g_seek_offset_us += 5000000; // +5 seconds
                    g_seek_request = 1;
                    seek_requested = 1;
                }
                if (GetAsyncKeyState(VK_LEFT) & 0x8000) {
                    g_seek_offset_us -= 5000000; // -5 seconds
                    g_seek_request = 1;
                    seek_requested = 1;
                }
                
                if (seek_requested) {
                    last_key_time = current_time;
                    EnterCriticalSection(&g_event_buffer.cs);
                    WakeConditionVariable(&g_event_buffer.not_full);
                    LeaveCriticalSection(&g_event_buffer.cs);
                }
                
                LeaveCriticalSection(&g_seek_cs);
            }
        }
        
        // Display status
        if (g_producer_finished && g_event_buffer.head == g_event_buffer.tail) {
            if (!playback_finished_msg) {
                printf("\rPlayback finished. Press ESC to exit or arrows to seek.    ");
                fflush(stdout);
                playback_finished_msg = 1;
            }
        } else {
            playback_finished_msg = 0;
            
            EnterCriticalSection(&g_event_buffer.cs);
            long head = g_event_buffer.head;
            long tail = g_event_buffer.tail;
            double seconds_ahead = 0.0;
            
            if (head != tail) {
                uint64_t last_event_time_us = g_event_buffer.events[(tail - 1 + EVENT_BUFFER_CAPACITY) % EVENT_BUFFER_CAPACITY].playback_time_us;
                if (last_event_time_us > g_consumer_playback_time_us) {
                    seconds_ahead = (last_event_time_us - g_consumer_playback_time_us) / 1000000.0;
                }
            }
            
            double current_time_s = g_consumer_playback_time_us / 1000000.0;
            long buffer_usage = (tail >= head) ? (tail - head) : (EVENT_BUFFER_CAPACITY - (head - tail));
            double buffer_percent = (buffer_usage * 100.0) / EVENT_BUFFER_CAPACITY;
            
            LeaveCriticalSection(&g_event_buffer.cs);
            
            printf("\rTime: %6.2fs | Buffer: %6.2fs ahead (%4.1f%%)", 
                   current_time_s, seconds_ahead, buffer_percent);
            fflush(stdout);
        }
        
        Sleep(50);
    }
    
    printf("\n\nStopping playback...\n");
    
    // Signal threads to stop
    g_stop = 1;
    WakeAllConditionVariable(&g_event_buffer.not_empty);
    WakeAllConditionVariable(&g_event_buffer.not_full);
    
    // Wait for threads to complete
    HANDLE threads[] = { producer_handle, consumer_handle };
    DWORD wait_result = WaitForMultipleObjects(2, threads, TRUE, 3000);
    
    if (wait_result == WAIT_TIMEOUT) {
        printf("Warning: Threads did not exit cleanly, terminating...\n");
        TerminateThread(producer_handle, 0);
        TerminateThread(consumer_handle, 0);
    }
    
    CloseHandle(producer_handle);
    CloseHandle(consumer_handle);
    
    // Reset MIDI device
    for (int i = 0; i < 16; i++) {
        send_midi_msg(MIDI_CONTROL_CHANGE | i | (123 << 8)); // All notes off
        send_midi_msg(MIDI_CONTROL_CHANGE | i | (121 << 8)); // Reset all controllers
    }
    
    // Cleanup
    free(tracks);
    cleanup_memory_mapping();
    midiOutClose(g_hMidiOut);
    g_hMidiOut = NULL;
    
    DeleteCriticalSection(&g_midi_cs);
    DeleteCriticalSection(&g_seek_cs);
    DeleteCriticalSection(&g_event_buffer.cs);
    
    printf("Cleanup complete. Goodbye!\n");
    return 0;
}

int main(int argc, char *argv[]) {
    if (argc != 2) {
        printf("Optimized MIDI Player v4.0 (Memory-Mapped I/O)\n");
        printf("===============================================\n");
        printf("Usage: %s <midi_file>\n", argv[0]);
        printf("\nControls:\n");
        printf("- ESC: Exit\n");
        printf("- Left/Right Arrows: Seek backward/forward 5 seconds\n");
        return 1;
    }
    
    return play_midi_file(argv[1]);
}